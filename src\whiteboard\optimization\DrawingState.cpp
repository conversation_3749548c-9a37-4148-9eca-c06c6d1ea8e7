#include "DrawingState.h"
#include "../tools/ShapeToolManager.h"

DrawingState::DrawingState()
{
}

void DrawingState::setToolType(ToolType toolType)
{
    m_toolType = toolType;
}

void DrawingState::setPen(const QPen& pen)
{
    m_pen = pen;
}

void DrawingState::setBrush(const QBrush& brush)
{
    m_brush = brush;
}


void DrawingState::startDrawing(const QPointF& startPoint)
{
    m_isDrawing = true;
    m_startPoint = startPoint;
    m_currentPoint = startPoint;
    m_lastPoint = startPoint;

    m_currentPath = QPainterPath();
    m_currentPath.moveTo(startPoint);
    if (isFreeDrawTool(m_toolType) || m_toolType == ToolType::Lasso) {
        m_freeDrawPathUnfinished.append(m_currentPath);
    }
}

void DrawingState::continueDrawing(const QPointF& point)
{
    m_lastPoint = m_currentPoint;
    m_currentPoint = point;

    addPoint(point);
    
}

void DrawingState::finishDrawing(const QPointF& point)
{
    addPoint(point);
    m_isDrawing = false;
}

void DrawingState::addPoint(const QPointF& point)
{
    if (isFreeDrawTool(m_toolType) || m_toolType == ToolType::Lasso) {
        m_currentPath.lineTo(point);
        QPainterPath tempPath = QPainterPath();
        tempPath.moveTo(m_lastPoint);
        tempPath.lineTo(point);
        m_freeDrawPathUnfinished.append(tempPath);
        
    } else {
        ShapeToolManager* manager = ShapeToolManager::instance();
        if (manager && manager->hasToolType(m_toolType)) {
            m_currentPath = manager->createPath(m_toolType, m_startPoint, point);
        }
    }
}

QPainterPath DrawingState::getCurrentPath() const
{
    return m_currentPath;
}

QPainterPath DrawingState::getFreeDrawPathUnfinished() const
{
    // 将所有未完成的先取出来然后合并成一个
    QPainterPath mergedPath;
    QPainterPath firstPath;
    while (!m_freeDrawPathUnfinished.isEmpty())
    {
        mergedPath.addPath(firstPath);
        m_freeDrawPathUnfinished.removeFirst(); // 删除首条数据
        firstPath = m_freeDrawPathUnfinished.first();
    }
    return mergedPath;
}