#include "WhiteBoardWidget.h"
#include "../constants/DrawingConstants.h"
#include "WhiteBoardScene.h"
#include "../graphics/DrawItem.h"
#include "../utils/QtPathClipper.h"
#include "../utils/DrawingPerformanceProfiler.h"
#include "../ui/SelectionUIManager.h"
#include "../ui/SelectionUITypes.h"
#include "../commands/CommandManager.h"
#include "../commands/Command.h"
#include "../optimization/OptimizedDrawingState.h"
#include "../tools/ShapeToolManager.h"
#include "../constants/DrawingConstants.h"

#include <QPainter>
#include <QApplication>
#include <QCoreApplication>
#include <QScreen>
#include <QDebug>
#include <QStyleOptionGraphicsItem>
#include <QGraphicsItem>
#include <QScrollBar>
#include <QWheelEvent>
#include <QFile>
#include <QTimer>
#include <QtMath>
#include <QMouseEvent>
#include <QTouchEvent>
#include <QElapsedTimer>
#include <QOpenGLWidget>
#include <QOpenGLContext>
#include <QSurfaceFormat>
#include <QtMath>

WhiteBoardWidget::WhiteBoardWidget(QWidget* parent)
    : QGraphicsView(parent)
    , m_scene(nullptr)
    , m_currentTool(ToolType::FreeDraw)
    , m_currentPen(Qt::black, 2.0, Qt::SolidLine, Qt::RoundCap, Qt::RoundJoin)
    , m_currentBrush(Qt::NoBrush)
{
    // ==================== OpenGL视口配置 ====================
    setupOpenGLViewport();

    // DRAWING_PROFILER_ENABLE(false);

    // 设置基本属性
    setRenderHint(QPainter::Antialiasing, true);
    setDragMode(QGraphicsView::NoDrag);
    setVerticalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    setHorizontalScrollBarPolicy(Qt::ScrollBarAlwaysOff);

    setViewportUpdateMode(QGraphicsView::MinimalViewportUpdate);

    setCacheMode(QGraphicsView::CacheBackground);

    setOptimizationFlag(QGraphicsView::IndirectPainting, true);

    setAttribute(Qt::WA_TranslucentBackground);
    setAutoFillBackground(false);
    setStyleSheet("QGraphicsView { background: transparent; }");

    if (QApplication::primaryScreen()) {
        m_devicePixelRatio = QApplication::primaryScreen()->devicePixelRatio();
    }

    // 启用触摸事件
    setAttribute(Qt::WA_AcceptTouchEvents, true);

    // 加载橡皮擦图片
    loadEraserImage();

    // 初始化SelectionUIManager
    m_selectionUIManager = new SelectionUIManager(this);
    m_selectionUIManager->setView(this);

    // 初始化擦除处理定时器
    m_erasureProcessTimer = new QTimer(this);
    m_erasureProcessTimer->setSingleShot(false);
    m_erasureProcessTimer->setInterval(20); // 20ms间隔
    connect(m_erasureProcessTimer, &QTimer::timeout, this, &WhiteBoardWidget::processNextErasurePath);

}

WhiteBoardWidget::~WhiteBoardWidget()
{
    // 清理SVG渲染器
    if (m_eraserSvgRenderer) {
        delete m_eraserSvgRenderer;
        m_eraserSvgRenderer = nullptr;
    }

    cancelAllDrawing();
    clearSelection();
}

// 场景设置
void WhiteBoardWidget::setWhiteBoardScene(WhiteBoardScene* scene)
{
    if (m_scene == scene) {
        return;
    }

    // 断开旧场景的连接
    if (m_scene) {
        disconnect(m_scene, nullptr, this, nullptr);
    }

    m_scene = scene;
    setScene(scene);

    // 初始化CommandManager
    if (scene) {
        CommandManager* cmdManager = CommandManager::instance();
        if (cmdManager && !cmdManager->isInitialized()) {
            cmdManager->initialize(scene);
        }

        // 连接历史层变化信号以自动使缓存失效
        connect(scene, &WhiteBoardScene::historyLayerChanged,
                this, &WhiteBoardWidget::invalidateHistoryCache);

        connect(scene, &WhiteBoardScene::itemAdded,
                this, &WhiteBoardWidget::invalidateHistoryCache);

        connect(scene, &WhiteBoardScene::itemRemoved,
                this, &WhiteBoardWidget::invalidateHistoryCache);
    }


}

// 工具设置接口
void WhiteBoardWidget::setCurrentTool(ToolType tool)
{
    if (m_currentTool != tool) {
        // 切换工具时取消当前绘制
        cancelAllDrawing();

        // 如果从选择工具切换到绘制工具，清理选择状态
        if (m_currentTool == ToolType::Lasso && tool != ToolType::Lasso) {
            clearSelection();
        }

        m_currentTool = tool;

        emit toolChanged(tool);
    }
}

void WhiteBoardWidget::setPen(const QPen& pen)
{
    m_currentPen = pen;

}

void WhiteBoardWidget::setBrush(const QBrush& brush)
{
    m_currentBrush = brush;
}

// 绘制接口实现
void WhiteBoardWidget::startDrawing(int touchId, const QPointF& point)
{
    DRAWING_TIMER("startDrawing");

    if (m_currentTool == ToolType::PassThrough) {
        return;
    }

    if (isFreeDrawTool(m_currentTool) || m_currentTool == ToolType::Lasso) {
        qreal dpr = devicePixelRatio();
        // 设置widget的大小
        m_freeDrawingsCache = QPixmap(this->size());
        m_freeDrawingsCache.setDevicePixelRatio(dpr);
        m_freeDrawingsCache.fill(Qt::transparent);
    }

    if (m_currentTool == ToolType::Lasso) {
        if (m_specialToolTouchId == SPECIAL_TOOL_TOUCH_ID) {
            m_specialToolTouchId = touchId;
            startLassoSelection(point);
        }
        return;
    }

    if (m_currentTool == ToolType::Eraser) {
        if (m_specialToolTouchId == SPECIAL_TOOL_TOUCH_ID) {
            m_specialToolTouchId = touchId;
            startErasing(point);
        }
        return;
    }

    if (!isMultiTouchAllowed(touchId)) {
        return;
    }

    DrawingState& state = m_activeDrawings[touchId];

    // 配置绘制状态
    QPen pen = m_currentPen;
    if (m_currentTool == ToolType::DashedLine) {
        pen.setStyle(Qt::CustomDashLine);
        pen.setDashPattern(DrawingConstants::CUSTOM_DASH_PATTERN);
    }

    state.setToolType(m_currentTool);
    state.setPen(pen);
    state.setBrush(m_currentBrush);

    // 开始绘制
    state.startDrawing(point);

    update();
}

void WhiteBoardWidget::continueDrawing(int touchId, const QPointF& point)
{
    DRAWING_TIMER("continueDrawing");
    if (m_currentTool == ToolType::Lasso) {
        if (m_specialToolTouchId == touchId) {
            continueLassoSelection(point);
        }
        return;
    }

    if (m_currentTool == ToolType::Eraser) {
        if (m_specialToolTouchId == touchId) {
            continueErasing(point);
        }
        return;
    }

    if (m_currentTool == ToolType::PassThrough) {
        return;
    }

    if (!m_activeDrawings.contains(touchId)) {

        return;
    }

    DrawingState& state = m_activeDrawings[touchId];
    if (!state.isDrawing()) {
        return;
    }



    // 使用优化的绘制状态继续绘制
    state.continueDrawing(point);

    if (state.getPen().style() == Qt::CustomDashLine) {
        // 虚线自由绘制会导致抖动，所以先全部重绘
        update();
        return;
    }
    
    update();
}

void WhiteBoardWidget::finishDrawing(int touchId)
{
    if (m_currentTool == ToolType::Lasso) {
        if (m_specialToolTouchId == touchId) {
            m_specialToolTouchId = SPECIAL_TOOL_TOUCH_ID;
            finishLassoSelection();
        }
        return;
    }

    if (m_currentTool == ToolType::Eraser) {
        if (m_specialToolTouchId == touchId) {
            m_specialToolTouchId = SPECIAL_TOOL_TOUCH_ID;
            finishErasing();
        }
        return;
    }

    if (m_currentTool == ToolType::PassThrough) {
        return;
    }

    if (!m_activeDrawings.contains(touchId)) return;

    DrawingState& state = m_activeDrawings[touchId];

    QGraphicsItem* item = createDrawItem(state);
    if (item && m_scene) {
        CommandManager* cmdManager = CommandManager::instance();
        if (cmdManager && cmdManager->isInitialized()) {
            cmdManager->addItem(item);
        } else {
            m_scene->addGraphicsItem(item);
            item->setZValue(0);
        }

        invalidateHistoryCache();
    }

    m_activeDrawings.remove(touchId);

    update();
    // 强制立即重绘，确保绘制的图形立即显示
    repaint();
}

void WhiteBoardWidget::cancelDrawing(int touchId)
{
    if (m_activeDrawings.contains(touchId)) {
        m_activeDrawings.remove(touchId);
        update();
    }
}

void WhiteBoardWidget::cancelAllDrawing()
{
    // 清理所有活动绘制状态
    if (!m_activeDrawings.isEmpty()) {
        m_activeDrawings.clear();
    }

    // 清理选择状态
    clearSelection();

    // 清理橡皮擦状态
    DrawingConstants::setEraseStatus(false);

    update();
}

// 选择功能实现
void WhiteBoardWidget::startLassoSelection(const QPointF& point)
{
    clearSelection();

    m_lassoPath.clear();
    m_lassoPath.append(point);

    m_lassoPathBuilder.setToolType(ToolType::Lasso);
    m_lassoPathBuilder.startPath(point);

    update();
}

void WhiteBoardWidget::continueLassoSelection(const QPointF& point)
{
    
    m_lassoPath.append(point);

    m_lassoPathBuilder.addPoint(point);

    update();
}

void WhiteBoardWidget::finishLassoSelection()
{
    if (m_lassoPath.size() >= 1) {
        m_lassoPathBuilder.finishPath();
        updateSelection();

        if (!m_selectedItems.isEmpty() && m_selectionUIManager) {
            m_selectionUIManager->showSelectionUI(m_selectedItems);
        }
    }

    // 清除套索路径，防止继续显示
    m_lassoPath.clear();
    update();
}

void WhiteBoardWidget::clearSelection()
{
    m_selectedItems.clear();
    m_lassoPath.clear();

    if (m_selectionUIManager) {
        m_selectionUIManager->hideSelectionUI();
    }

    update();
    emit selectionChanged();
}

// 核心绘制方法实现
void WhiteBoardWidget::drawForeground(QPainter* painter, const QRectF& rect)
{
    DRAWING_TIMER("drawForeground");

    setupEnhancedRenderingHints(painter);

    // 绘制当前活动的绘制内容（临时绘制）
    drawActiveDrawings(painter, rect);

    // 绘制UI元素
    drawUIElements(painter, rect);
}

void WhiteBoardWidget::drawItems(QPainter* painter, int numItems,
                              QGraphicsItem* items[], const QStyleOptionGraphicsItem options[])
{
    DRAWING_TIMER("drawItems");
    drawHistoryLayer(painter);
}

void WhiteBoardWidget::drawBackground(QPainter* painter, const QRectF& rect)
{
    drawBackgroundColor(painter, rect);
    // drawGrid(painter, rect);  // 禁用网格绘制
}

// 事件处理
bool WhiteBoardWidget::event(QEvent* event)
{
    switch (event->type()) {
    case QEvent::TouchBegin:
    case QEvent::TouchUpdate:
    case QEvent::TouchEnd:
        // 处理触屏事件并阻止其转换为鼠标事件
        if (handleTouchEvent(static_cast<QTouchEvent*>(event))) {
            event->accept();  // 明确接受事件
            return true;      // 阻止事件继续传播，防止转换为鼠标事件
        }
        break;
    default:
        break;
    }
    return QGraphicsView::event(event);
}

void WhiteBoardWidget::mousePressEvent(QMouseEvent* event)
{
    // 如果存在活动的触摸绘制，不响应鼠标事件
    if (hasTouchDrawing()) {
        return;
    }

    QPointF scenePos = mapToSceneWithDPI(event->position());

    if (m_selectionUIManager && m_selectionUIManager->isSelectionUIVisible()) {
        AnchorType anchorType;
        ToolbarAction toolbarAction;
        HitTestResult hitResult = m_selectionUIManager->hitTest(scenePos, anchorType, toolbarAction);

        if (hitResult != HitTestResult::None) {
            m_selectionUIManager->startInteraction(scenePos, hitResult, anchorType, toolbarAction);
            return; 
        } else {
            m_selectionUIManager->forceCleanupSelection();
        }
    }

    startDrawing(-1, scenePos); // 使用-1作为鼠标的touchId
    QGraphicsView::mousePressEvent(event);
}

void WhiteBoardWidget::mouseMoveEvent(QMouseEvent* event)
{
    // 如果存在活动的触摸绘制，不响应鼠标事件
    if (hasTouchDrawing()) {
        return;
    }

    QPointF scenePos = mapToSceneWithDPI(event->position());

    if (m_selectionUIManager && m_selectionUIManager->isSelectionUIVisible()) {
        m_selectionUIManager->updateInteraction(scenePos);
        invalidateHistoryCache();
        return;
    }

    continueDrawing(-1, scenePos);
    QGraphicsView::mouseMoveEvent(event);
}

void WhiteBoardWidget::mouseReleaseEvent(QMouseEvent* event)
{
    if (hasTouchDrawing()) {
        return;
    }

    QPointF scenePos = mapToSceneWithDPI(event->position());

    if (m_selectionUIManager && m_selectionUIManager->isSelectionUIVisible()) {
        m_selectionUIManager->finishInteraction(scenePos);
        invalidateHistoryCache();
        return;
    }

    finishDrawing(-1);
    QGraphicsView::mouseReleaseEvent(event);
}

void WhiteBoardWidget::wheelEvent(QWheelEvent* event)
{
    event->ignore();

}

void WhiteBoardWidget::scrollContentsBy(int dx, int dy)
{
    if (dx != 0 || dy != 0) {
        return;
    }

    QGraphicsView::scrollContentsBy(dx, dy);
}


// 图形项创建
QGraphicsItem* WhiteBoardWidget::createDrawItem(const DrawingState& state)
{
    ToolType toolType = state.getToolType();
    QPainterPath path = state.getCurrentPath();
    QPen enhancedPen = getAntialiasedPen(state.getPen());
    DrawItem* item = new DrawItem(path, enhancedPen, state.getBrush(), toolType);
    return item;
}

// UI绘制方法
void WhiteBoardWidget::drawActiveDrawings(QPainter* painter, const QRectF& clipRect)
{
    DRAWING_TIMER("drawActiveDrawings");

    for (auto it = m_activeDrawings.begin(); it != m_activeDrawings.end(); ++it) {
        DrawingState& state = it.value();
        if (state.isDrawing()) {
            QPainterPath originalPath = state.getCurrentPath();
            if (!originalPath.isEmpty()) {
                if (clipRect.isEmpty() || originalPath.boundingRect().intersects(clipRect)) {
                    ToolType toolType = state.getToolType();
                    if (isFreeDrawTool(toolType) || toolType == ToolType::Lasso) {
                        QPainter cachePainter(&m_freeDrawingsCache);
                        cachePainter.setRenderHints(painter->renderHints());
                        cachePainter.setTransform(painter->transform(), true);
                        FeatheringRenderer::drawPathWithFeathering(&cachePainter, originalPath, state.getPen(), state.getBrush(), toolType);
                        painter->drawPixmap(QPoint(0, 0), m_freeDrawingsCache);
                    } else {
                        // 其他工具直接绘制
                        FeatheringRenderer::drawPathWithFeathering(painter, originalPath, state.getPen(), state.getBrush(), toolType);
                    }
                }
            }
        }
    }

}


void WhiteBoardWidget::drawUIElements(QPainter* painter, const QRectF& rect)
{
    Q_UNUSED(rect)

    // 绘制套索路径
    drawLassoPath(painter);

    // 绘制选择框UI
    if (m_selectionUIManager) {
        m_selectionUIManager->paintSelectionUI(painter);
    }

    // 绘制橡皮擦
    drawEraser(painter);
}

void WhiteBoardWidget::drawLassoPath(QPainter* painter)
{
    if (m_lassoPath.size() > 1) {
        QPen lassoPen(RenderConstants::SELECTION_COLOR, RenderConstants::SELECTION_BORDER_WIDTH(), Qt::DashLine);
        painter->setPen(lassoPen);
        painter->setBrush(Qt::NoBrush);

        QPainterPath optimizedPath = m_lassoPathBuilder.getCurrentPath();
        if (!optimizedPath.isEmpty()) {
            painter->drawPath(optimizedPath);

        } else {
            QPainterPath fallbackPath;
            fallbackPath.moveTo(m_lassoPath.first());
            for (int i = 1; i < m_lassoPath.size(); ++i) {
                fallbackPath.lineTo(m_lassoPath[i]);
            }
            painter->drawPath(fallbackPath);
        }
    }
}



void WhiteBoardWidget::drawBackgroundColor(QPainter* painter, const QRectF& rect)
{
    // 设置背景为透明，不绘制任何背景色
    // painter->fillRect(rect, m_backgroundColor);
    Q_UNUSED(painter)
    Q_UNUSED(rect)
}

// 分层缓存实现
void WhiteBoardWidget::updateHistoryLayerCache()
{
    if (!m_scene) {
        return;
    }

    // 获取场景中的所有历史图形项
    QList<QGraphicsItem*> historyItems = m_scene->items();
    if (historyItems.isEmpty()) {
        m_historyLayerCache = QPixmap();
        m_historyLayerDirty = false;
        return;
    }

    // 计算所有历史图形的边界
    QRectF historyBounds;
    for (QGraphicsItem* item : historyItems) {
        if (item && item->isVisible()) {
            historyBounds = historyBounds.united(item->sceneBoundingRect());
        }
    }

    if (historyBounds.isEmpty()) {
        m_historyLayerCache = QPixmap();
        m_historyLayerDirty = false;
        return;
    }

    qreal dpr = devicePixelRatio();
    QSize cacheSize = (historyBounds.size() * dpr).toSize();

    m_historyLayerCache = QPixmap(cacheSize);
    m_historyLayerCache.setDevicePixelRatio(dpr);
    m_historyLayerCache.fill(Qt::transparent);

    // 在缓存上绘制所有历史图形
    QPainter cachePainter(&m_historyLayerCache);

    setupEnhancedRenderingHints(&cachePainter);

    cachePainter.translate(-historyBounds.topLeft());

    // 绘制所有历史图形项
    DrawingConstants::setDrawClear(false);
    QStyleOptionGraphicsItem option;
    for (QGraphicsItem* item : historyItems) {
        if (item && item->isVisible()) {
            cachePainter.save();
            cachePainter.setTransform(item->sceneTransform(), true);
            item->paint(&cachePainter, &option, nullptr);
            cachePainter.restore();
        }
    }
    DrawingConstants::setDrawClear(true);

    cachePainter.end();

    m_lastHistoryBounds = historyBounds;
    m_historyLayerDirty = false;


}

void WhiteBoardWidget::drawHistoryLayer(QPainter* painter)
{
    // 如果历史层缓存需要更新
    if (m_historyLayerDirty || m_historyLayerCache.isNull()) {
        updateHistoryLayerCache();
    }

    // 绘制缓存的历史层
    if (!m_historyLayerCache.isNull()) {
        painter->save();
        QPointF topLeft = m_lastHistoryBounds.topLeft();

        // 绘制缓存的历史层
        painter->drawPixmap(topLeft, m_historyLayerCache);

        painter->restore();
    }
}

void WhiteBoardWidget::invalidateHistoryCache()
{
    m_historyLayerDirty = true;
    update();
}

void WhiteBoardWidget::clearHistoryCache()
{
    m_historyLayerCache = QPixmap();
    m_historyLayerDirty = true;
    m_lastHistoryBounds = QRectF();

    update();
}

void WhiteBoardWidget::drawGrid(QPainter* painter, const QRectF& rect)
{
    painter->setPen(QPen(m_gridColor, 1));

    int startX = static_cast<int>(rect.left() / m_gridSize) * m_gridSize;
    int startY = static_cast<int>(rect.top() / m_gridSize) * m_gridSize;

    for (int x = startX; x <= rect.right(); x += m_gridSize) {
        painter->drawLine(x, rect.top(), x, rect.bottom());
    }

    for (int y = startY; y <= rect.bottom(); y += m_gridSize) {
        painter->drawLine(rect.left(), y, rect.right(), y);
    }
}

// 选择处理
void WhiteBoardWidget::updateSelection()
{
    if (!m_scene || m_lassoPath.size() < 1) {
        return;
    }

    m_selectedItems.clear();

    // 创建套索路径
    QPainterPath lassoPath;
    lassoPath.moveTo(m_lassoPath.first());
    for (int i = 1; i < m_lassoPath.size(); ++i) {
        lassoPath.lineTo(m_lassoPath[i]);
    }
    lassoPath.closeSubpath();

    m_selectedItems = performLassoSelection(lassoPath, m_lassoPath);

    emit selectionChanged();
}

bool WhiteBoardWidget::isPointInLasso(const QPointF& point)
{
    if (m_lassoPath.size() < 3) {
        return false;
    }

    QPainterPath path;
    path.moveTo(m_lassoPath.first());
    for (int i = 1; i < m_lassoPath.size(); ++i) {
        path.lineTo(m_lassoPath[i]);
    }
    path.closeSubpath();

    return path.contains(point);
}



// 坐标转换
QPointF WhiteBoardWidget::mapToSceneWithDPI(const QPointF& viewPoint)
{
    // QPointF scenePoint = mapToScene(static_cast<int>(viewPoint.x()), static_cast<int>(viewPoint.y()));

    // qreal deltaX = viewPoint.x() - static_cast<int>(viewPoint.x());
    // qreal deltaY = viewPoint.y() - static_cast<int>(viewPoint.y());

    // QTransform transform = this->transform();
    // QPointF deltaScene = transform.map(QPointF(deltaX, deltaY)) - transform.map(QPointF(0, 0));

    // return scenePoint + deltaScene;
    return viewPoint;
}

// 事件处理
bool WhiteBoardWidget::handleTouchEvent(QTouchEvent* event)
{
    const auto& touchPoints = event->points();

    if (touchPoints.isEmpty()) {
        return false;
    }

    // 检查是否有鼠标绘制正在进行（touchId为-1）
    bool hasMouseDrawing = m_activeDrawings.contains(-1) && m_activeDrawings[-1].isDrawing();
    if (hasMouseDrawing) {
        qDebug() << "[MULTITOUCH] 鼠标正在绘制，忽略触摸事件";
        return false;
    }


    bool eventHandled = false;
    for (const auto& point : touchPoints) {
        // 只处理有效的触控点ID（>= 0）
        if (point.id() < 0) {
            continue;
        }

        QPointF scenePos = mapToSceneWithDPI(point.position());
        int touchId = point.id();


        switch (point.state()) {
        case QEventPoint::Pressed:
            if (m_selectionUIManager && m_selectionUIManager->isSelectionUIVisible()) {
                AnchorType anchorType;
                ToolbarAction toolbarAction;
                HitTestResult hitResult = m_selectionUIManager->hitTest(scenePos, anchorType, toolbarAction);

                if (hitResult != HitTestResult::None) {
                    m_selectionUIManager->startInteraction(scenePos, hitResult, anchorType, toolbarAction);
                    eventHandled = true;
                    break; 
                } else {
                    m_selectionUIManager->forceCleanupSelection();
                }
            }

            startDrawing(touchId, scenePos);
            eventHandled = true;
            break;
        case QEventPoint::Updated:
            if (m_selectionUIManager && m_selectionUIManager->isSelectionUIVisible()) {
                m_selectionUIManager->updateInteraction(scenePos);
                invalidateHistoryCache();
                eventHandled = true;
                break;
            }

            continueDrawing(touchId, scenePos);
            eventHandled = true;
            break;
        case QEventPoint::Released:
            if (m_selectionUIManager && m_selectionUIManager->isSelectionUIVisible()) {
                m_selectionUIManager->finishInteraction(scenePos);
                invalidateHistoryCache();
                eventHandled = true;
                break;
            }

            finishDrawing(touchId);
            eventHandled = true;
            break;
        case QEventPoint::Stationary:
            eventHandled = true;
            break;
        default:
            break;
        }
    }

    return eventHandled;
}



// 橡皮擦功能实现
void WhiteBoardWidget::startErasing(const QPointF& point)
{
    DrawingConstants::setEraseStatus(true);
    m_eraserPosition = point;

    // 清空之前的擦除状态
    m_eraserRects.clear();
    m_itemSnapshots.clear();
    DrawingConstants::clearAllEraserData();

    // 记录擦除前的场景状态
    if (m_scene) {
        m_eraseBeforeStates.clear();
        QList<QGraphicsItem*> allItems = m_scene->items();
        for (QGraphicsItem* item : allItems) {
            m_eraseBeforeStates.append(GraphicsItemState::fromGraphicsItem(item));

            // 为自由绘制图形项创建快照
            if (item->type() == DrawItem::DrawItemType) {
                DrawItem* drawItem = qgraphicsitem_cast<DrawItem*>(item);
                if (drawItem && isFreeDrawTool(drawItem->toolType())) {
                    m_itemSnapshots.append(createItemSnapshot(drawItem));
                }
            }
        }
    }


    if (!m_erasureProcessTimer->isActive()) {
        m_erasureProcessTimer->start();
    }

    // 开始擦除操作
    eraseAtPosition(point);

    update();
}

void WhiteBoardWidget::continueErasing(const QPointF& point)
{
    m_eraserPosition = point;

    // 继续擦除操作
    eraseAtPosition(point);

    update();
}

void WhiteBoardWidget::finishErasing()
{

    // 停止定时器处理
    if (m_erasureProcessTimer->isActive()) {
        m_erasureProcessTimer->stop();
    }

    // 处理剩余的擦除路径
    while (DrawingConstants::hasClipPaths()) {
        QPainterPath clipPath = DrawingConstants::takeFirstClipPath();
        if (!clipPath.isEmpty()) {
            QRectF eraserRect = clipPath.boundingRect();

            for (int i = 0; i < m_itemSnapshots.size(); ++i) {
                DrawItemSnapshot& snapshot = m_itemSnapshots[i];

                QRectF pathBounds = snapshot.path.boundingRect();
                qreal penWidth = snapshot.pen.widthF();
                pathBounds.adjust(-penWidth/2, -penWidth/2, penWidth/2, penWidth/2);

                if (pathBounds.intersects(eraserRect)) {
                    QPainterPath resultPath = QtPathClipper::subtractPath(snapshot.path, clipPath);

                    if (resultPath != snapshot.path) {
                        snapshot.path = resultPath;
                    }
                }
            }
        }
    }

    // 与场景中的items进行比对，删除和添加
    applySnapshotsToScene();

    if (m_scene && !m_eraseBeforeStates.isEmpty()) {
        QList<GraphicsItemState> afterStates;
        QList<QGraphicsItem*> allItems = m_scene->items();
        for (QGraphicsItem* item : allItems) {
            afterStates.append(GraphicsItemState::fromGraphicsItem(item));
        }

        CommandManager* cmdManager = CommandManager::instance();
        if (cmdManager && cmdManager->isInitialized()) {
            Command* eraseCommand = CommandFactory::fromStateDifference(
                m_eraseBeforeStates, afterStates, "橡皮擦操作");
            if (eraseCommand) {
                cmdManager->executeCommand(eraseCommand, false); // 已经执行，只需加入栈
            }
        }

        m_eraseBeforeStates.clear();
    }

    // 清理擦除状态
    m_eraserRects.clear();
    m_itemSnapshots.clear();
    DrawingConstants::clearAllEraserData();
    DrawingConstants::setEraseStatus(false);
    update();
}

void WhiteBoardWidget::eraseAtPosition(const QPointF& point)
{
    if (!m_scene) return;

    QRectF eraserRect(point.x() - m_eraserSize.width()/2,
                      point.y() - m_eraserSize.height()/2,
                      m_eraserSize.width(),
                      m_eraserSize.height());

    // 记录橡皮擦区域，用于异步切割
    m_eraserRects.append(eraserRect);

    // 添加到全局擦除路径
    DrawingConstants::addEraserRect(eraserRect);
    invalidateHistoryCache();
}



void WhiteBoardWidget::drawEraser(QPainter* painter)
{
    if (!DrawingConstants::getEraseStatus()) {
        return;
    }

    setupEnhancedRenderingHints(painter);

    QRectF eraserRect(m_eraserPosition.x() - m_eraserSize.width()/2,
                      m_eraserPosition.y() - m_eraserSize.height()/2,
                      m_eraserSize.width(),
                      m_eraserSize.height());

    if (m_eraserSvgRenderer && m_eraserSvgRenderer->isValid()) {
        m_eraserSvgRenderer->render(painter, eraserRect);
    } else if (!m_eraserPixmap.isNull()) {
        painter->drawPixmap(eraserRect, m_eraserPixmap, m_eraserPixmap.rect());
    } else {
        // 如果都加载失败，绘制一个简单的矩形作为后备
        QPen fallbackPen(QColor(128, 128, 128), 1.0, Qt::SolidLine);
        painter->setPen(fallbackPen);
        painter->setBrush(QBrush(QColor(200, 200, 200, 128)));
        painter->drawRect(eraserRect);

        // 绘制"E"字母表示橡皮擦
        painter->setPen(QPen(Qt::black));
        QFont font = painter->font();
        font.setPointSize(qMax(8, (int)(eraserRect.height() * 0.3)));
        painter->setFont(font);
        painter->drawText(eraserRect, Qt::AlignCenter, "E");
    }
}

void WhiteBoardWidget::loadEraserImage()
{
    QString svgPath = ":/whiteboard/images/eraser.svg";
    m_eraserSvgRenderer = new QSvgRenderer(svgPath, this);
}

// 橡皮擦属性设置实现
void WhiteBoardWidget::setEraserSize(const QSizeF& size)
{
    if (size.width() > 0 && size.height() > 0) {
        m_eraserSize = size;
    }
}

QSizeF WhiteBoardWidget::getEraserSize() const
{
    return m_eraserSize;
}

qreal WhiteBoardWidget::getEraserWidth() const
{
    return m_eraserSize.width();
}

// 命令系统接口实现
bool WhiteBoardWidget::canUndo() const
{
    CommandManager* cmdManager = CommandManager::instance();
    return cmdManager && cmdManager->isInitialized() && cmdManager->canUndo();
}

bool WhiteBoardWidget::canRedo() const
{
    CommandManager* cmdManager = CommandManager::instance();
    return cmdManager && cmdManager->isInitialized() && cmdManager->canRedo();
}

bool WhiteBoardWidget::undo()
{
    clearSelection();

    CommandManager* cmdManager = CommandManager::instance();
    if (cmdManager && cmdManager->isInitialized()) {
        bool result = cmdManager->undo();
        if (result) {
            invalidateHistoryCache();
        }
        return result;
    }
    return false;
}

bool WhiteBoardWidget::redo()
{
    clearSelection();

    CommandManager* cmdManager = CommandManager::instance();
    if (cmdManager && cmdManager->isInitialized()) {
        bool result = cmdManager->redo();
        if (result) {
            invalidateHistoryCache();
        }
        return result;
    }
    return false;
}

void WhiteBoardWidget::clearHistory()
{
    CommandManager* cmdManager = CommandManager::instance();
    if (cmdManager && cmdManager->isInitialized()) {
        cmdManager->clear();
    }
}

// 异步切割相关方法实现
DrawItemSnapshot WhiteBoardWidget::createItemSnapshot(DrawItem* item)
{
    DrawItemSnapshot snapshot;
    if (!item) return snapshot;

    snapshot.originalItem = item;
    snapshot.path = item->path();
    snapshot.pen = item->pen();
    snapshot.brush = item->brush();
    snapshot.toolType = item->toolType();
    snapshot.position = item->pos();
    snapshot.transform = item->transform();
    snapshot.zValue = item->zValue();

    return snapshot;
}

void WhiteBoardWidget::processNextErasurePath()
{
    if (!DrawingConstants::hasClipPaths()) {
        return;
    }

    QPainterPath clipPath = DrawingConstants::takeFirstClipPath();
    if (clipPath.isEmpty()) {
        return;
    }


    QRectF eraserRect = clipPath.boundingRect();

    for (int i = 0; i < m_itemSnapshots.size(); ++i) {
        DrawItemSnapshot& snapshot = m_itemSnapshots[i];

        QRectF pathBounds = snapshot.path.boundingRect();
        qreal penWidth = snapshot.pen.widthF();
        pathBounds.adjust(-penWidth/2, -penWidth/2, penWidth/2, penWidth/2);

        if (pathBounds.intersects(eraserRect)) {
            QPainterPath resultPath = QtPathClipper::subtractPath(snapshot.path, clipPath);
            if (resultPath != snapshot.path) {
                snapshot.path = resultPath;
            }
        }
    }
}

void WhiteBoardWidget::applySnapshotsToScene()
{
    if (!m_scene || m_itemSnapshots.isEmpty()) {
        return;
    }


    // 开启批量更新模式
    m_scene->enableBatchUpdates(true);

    //场景中的原始图形项进行比对
    for (const DrawItemSnapshot& snapshot : m_itemSnapshots) {
        DrawItem* originalItem = snapshot.originalItem;

        if (!m_scene->items().contains(originalItem)) {
            continue;
        }

        if (snapshot.path != originalItem->path()) {
            if (snapshot.path.isEmpty() || snapshot.path.elementCount() <= 1) {
                m_scene->removeGraphicsItem(originalItem);
            } else {
                m_scene->removeGraphicsItem(originalItem);
                QList<QPainterPath> resultPaths = splitPathIntoSubpaths(snapshot.path);
                for (const QPainterPath& path : resultPaths) {
                    if (!path.isEmpty() && path.elementCount() > 1) {
                        createDrawItemFromPath(originalItem, path, snapshot.pen, snapshot.brush);
                    }
                }
            }
        }
    }

    // 关闭批量更新模式
    m_scene->enableBatchUpdates(false);

}

QGraphicsItem* WhiteBoardWidget::createDrawItemFromPath( DrawItem* originalItem, const QPainterPath& path, const QPen& pen, const QBrush& brush)
{
    if (path.isEmpty() || !m_scene) {
        return nullptr;
    }

    QPainterPath finalPath = path;
    QPen finalPen = pen;

    QPen enhancedPen = getAntialiasedPen(finalPen);
    DrawItem* newItem = new DrawItem(finalPath, enhancedPen, brush, originalItem->toolType());
    m_scene->addGraphicsItem(newItem);

    return newItem;
}

// 将路径分割成独立的子路径
QList<QPainterPath> WhiteBoardWidget::splitPathIntoSubpaths(const QPainterPath& path)
{
    QList<QPainterPath> subpaths;

    if (path.isEmpty()) {
        return subpaths;
    }

    QPainterPath currentSubpath;

    for (int i = 0; i < path.elementCount(); ++i) {
        QPainterPath::Element element = path.elementAt(i);

        if (element.type == QPainterPath::MoveToElement) {
            // 如果当前子路径不为空，保存它
            if (!currentSubpath.isEmpty()) {
                subpaths.append(currentSubpath);
                currentSubpath = QPainterPath();
            }
            currentSubpath.moveTo(element.x, element.y);
        } else if (element.type == QPainterPath::LineToElement) {
            currentSubpath.lineTo(element.x, element.y);
        } else if (element.type == QPainterPath::CurveToElement) {
            // 处理贝塞尔曲线（需要下两个控制点）
            if (i + 2 < path.elementCount()) {
                QPainterPath::Element cp1 = path.elementAt(i + 1);
                QPainterPath::Element cp2 = path.elementAt(i + 2);
                currentSubpath.cubicTo(element.x, element.y, cp1.x, cp1.y, cp2.x, cp2.y);
                i += 2; // 跳过控制点
            }
        }
    }

    // 添加最后一个子路径
    if (!currentSubpath.isEmpty()) {
        subpaths.append(currentSubpath);
    }

    return subpaths;
}

// 多指绘制控制
bool WhiteBoardWidget::isMultiTouchAllowed(int touchId) const
{
    if (!m_multiTouchEnabled) {
        for (auto it = m_activeDrawings.begin(); it != m_activeDrawings.end(); ++it) {
            if (it.value().isDrawing()) {
                return false;
            }
        }
        return true;
    }

    if (m_currentTool != ToolType::FreeDraw) {
        for (auto it = m_activeDrawings.begin(); it != m_activeDrawings.end(); ++it) {
            if (it.value().isDrawing()) {
                return false;
            }
        }
        return true;
    }

    int activeTouchCount = 0;
    for (auto it = m_activeDrawings.begin(); it != m_activeDrawings.end(); ++it) {
        if (it.value().isDrawing()) {
            activeTouchCount++;
        }
    }
    if (activeTouchCount >= MAX_TOUCH_POINTS) {
        return false;
    }

    return true;
}

bool WhiteBoardWidget::hasTouchDrawing() const
{
    // 检查是否有非鼠标的活动绘制（touchId >= 0）
    bool hasTouch = false;

    for (auto it = m_activeDrawings.begin(); it != m_activeDrawings.end(); ++it) {
        if (it.value().isDrawing()) {
            if (it.key() >= 0) {
                hasTouch = true;
            }
        }
    }

    return hasTouch;
}

// 多指绘制控制接口
void WhiteBoardWidget::setMultiTouchEnabled(bool enabled)
{
    m_multiTouchEnabled = enabled;
    qDebug() << "[MULTITOUCH] Multi-touch" << (enabled ? "enabled" : "disabled");

    if (!enabled) {
        QList<int> touchIdsToRemove;
        bool keepFirst = true;

        for (auto it = m_activeDrawings.begin(); it != m_activeDrawings.end(); ++it) {
            if (it.value().isDrawing()) {
                if (keepFirst) {
                    keepFirst = false;

                } else {
                    touchIdsToRemove.append(it.key());
                }
            }
        }

        for (int touchId : touchIdsToRemove) {
            finishDrawing(touchId);

        }
    }
}

QList<QGraphicsItem*> WhiteBoardWidget::performLassoSelection(const QPainterPath& lassoPath, const QList<QPointF>& lassoPoints)
{
    QList<QGraphicsItem*> selectedItems;

    if (!m_scene || lassoPoints.isEmpty()) {
        return selectedItems;
    }

    // 特殊处理：当套索点少于等于2个时，使用点选择模式
    if (lassoPoints.size() <= 2) {
        QGraphicsItem* topItem = selectTopItemAtLassoPoints(lassoPoints);
        if (topItem) {
            selectedItems.append(topItem);
        }
        return selectedItems;
    }

    // 检查是否是直线套索路径
    if (isLassoPathLinear(lassoPoints)) {
        return selectItemsByLinearLasso(lassoPoints);
    }

    // 创建闭合路径
    QPainterPath closedPath = createClosedPath(lassoPath);

    // 步骤1：用边界框快速排除明显不在套索内的对象
    QRectF lassoRect = closedPath.boundingRect();
    QList<QGraphicsItem*> candidates = m_scene->items(lassoRect);


    // 对每个候选图形进行精确检测
    for (QGraphicsItem* item : candidates) {
        if (isGraphicSelectedByLasso(item, closedPath, lassoPoints)) {
            selectedItems.append(item);
        }
    }

    return selectedItems;
}

// 点选择模式：当套索点≤2时，选择Z-index最高的图形
QGraphicsItem* WhiteBoardWidget::selectTopItemAtLassoPoints(const QList<QPointF>& lassoPoints)
{
    if (!m_scene || lassoPoints.isEmpty()) {
        return nullptr;
    }

    // 计算检测点
    QPointF testPoint = lassoPoints.first();
    if (lassoPoints.size() == 2) {
        testPoint = (lassoPoints.first() + lassoPoints.last()) / 2.0;
    }

    // 获取该点处的所有图形项，按Z-order排序（最上面的在前）
    QList<QGraphicsItem*> itemsAtPoint = m_scene->items(testPoint, Qt::IntersectsItemShape, Qt::DescendingOrder);

    // 遍历所有候选项，找到最适合的
    for (QGraphicsItem* item : itemsAtPoint) {
        if (!item) continue;

        int itemType = item->type();
        if (itemType < QGraphicsItem::UserType) continue;

        // 将场景坐标转换为图形项的本地坐标
        QPointF localPoint = item->mapFromScene(testPoint);

        if (itemType == DrawItem::DrawItemType) {
            DrawItem* drawItem = qgraphicsitem_cast<DrawItem*>(item);
            if (drawItem && isFreeDrawTool(drawItem->toolType())) {
                // 对于自由绘制：简化检测，使用边界矩形
                if (item->contains(localPoint)) {

                    return item;
                }
            } else if (drawItem) {
                // 对于其他图形：判断在图形内部
                if (item->contains(localPoint)) {
                    return item;
                }
            }
        }
    }

    return nullptr;
}

// 检查套索路径是否是直线
bool WhiteBoardWidget::isLassoPathLinear(const QList<QPointF>& lassoPoints)
{
    if (lassoPoints.size() < 3) {
        return true;
    }

    // 检查所有点是否在一条直线上
    const qreal tolerance = 10.0;

    QPointF start = lassoPoints.first();
    QPointF end = lassoPoints.last();

    // 计算起点到终点的向量
    QPointF lineVector = end - start;
    qreal lineLength = std::sqrt(lineVector.x() * lineVector.x() + lineVector.y() * lineVector.y());

    if (lineLength < tolerance) {
        return true;
    }

    QPointF unitVector = lineVector / lineLength;

    // 检查中间的点是否都在直线附近
    for (int i = 1; i < lassoPoints.size() - 1; ++i) {
        QPointF point = lassoPoints[i];
        QPointF toPoint = point - start;

        // 计算点到直线的距离
        qreal projection = QPointF::dotProduct(toPoint, unitVector);
        QPointF projectedPoint = start + unitVector * projection;
        QPointF perpendicular = point - projectedPoint;
        qreal distance = std::sqrt(perpendicular.x() * perpendicular.x() + perpendicular.y() * perpendicular.y());

        if (distance > tolerance) {
            return false;
        }
    }

    return true;
}

// 创建闭合路径
QPainterPath WhiteBoardWidget::createClosedPath(const QPainterPath& openPath)
{
    QPainterPath closedPath = openPath;

    // 检查路径是否需要闭合
    if (!closedPath.isEmpty()) {
        // 检查起点和终点是否相同，如果不同则闭合路径
        QPointF startPoint = closedPath.pointAtPercent(0);
        QPointF endPoint = closedPath.currentPosition();

        // 使用小的容差值来比较浮点数
        const qreal tolerance = 1e-6;
        if (qAbs(startPoint.x() - endPoint.x()) > tolerance ||
            qAbs(startPoint.y() - endPoint.y()) > tolerance) {
            closedPath.closeSubpath();
        }
    }

    return closedPath;
}

QList<QGraphicsItem*> WhiteBoardWidget::selectItemsByLinearLasso(const QList<QPointF>& lassoPoints)
{
    QList<QGraphicsItem*> selectedItems;

    if (!m_scene || lassoPoints.size() < 2) {
        return selectedItems;
    }

    QPointF start = lassoPoints.first();
    QPointF end = lassoPoints.last();

    const qreal selectionWidth = 20.0;

    // 计算线段的方向向量和垂直向量
    QPointF direction = end - start;
    qreal length = std::sqrt(direction.x() * direction.x() + direction.y() * direction.y());

    if (length < 1.0) {
        // 线段太短，使用点选择
        QGraphicsItem* item = m_scene->itemAt(start, QTransform());
        if (item) {
            selectedItems.append(item);
        }
        return selectedItems;
    }

    QPointF unitDirection = direction / length;
    QPointF perpendicular(-unitDirection.y(), unitDirection.x()); // 垂直向量

    // 创建选择区域的四个角点
    QPointF offset = perpendicular * (selectionWidth / 2.0);
    QPolygonF selectionArea;
    selectionArea << (start + offset) << (start - offset) << (end - offset) << (end + offset);

    // 获取选择区域内的所有候选项
    QPainterPath selectionPath;
    selectionPath.addPolygon(selectionArea);
    QList<QGraphicsItem*> candidates = m_scene->items(selectionPath, Qt::IntersectsItemBoundingRect);


    for (QGraphicsItem* item : candidates) {
        if (item && item->type() >= QGraphicsItem::UserType) {
            selectedItems.append(item);
        }
    }

    return selectedItems;
}

bool WhiteBoardWidget::isGraphicSelectedByLasso(QGraphicsItem* item, const QPainterPath& lassoPath, const QList<QPointF>& lassoPoints)
{
    if (!item) return false;

    QRectF itemRect = item->sceneBoundingRect();

    // 检查边界矩形的中心点是否在套索内
    QPointF center = itemRect.center();
    if (lassoPath.contains(center)) {
        return true;
    }

    // 检查边界矩形的四个角点是否有任何一个在套索内
    QList<QPointF> corners = {
        itemRect.topLeft(),
        itemRect.topRight(),
        itemRect.bottomLeft(),
        itemRect.bottomRight()
    };

    for (const QPointF& corner : corners) {
        if (lassoPath.contains(corner)) {
            return true;
        }
    }

    if (lassoPath.intersects(itemRect)) {
        return true;
    }

    return false;
}


void WhiteBoardWidget::setupEnhancedRenderingHints(QPainter* painter) const
{
    if (!painter) return;

    QPainter::RenderHints enhancedHints = QPainter::Antialiasing |
                                          QPainter::SmoothPixmapTransform |
                                          QPainter::TextAntialiasing |
                                          QPainter::LosslessImageRendering;

    QPen currentPen = painter->pen();
    QPen enhancedPen = getAntialiasedPen(currentPen);
    painter->setPen(enhancedPen);
    painter->setRenderHints(enhancedHints);
}

QPen WhiteBoardWidget::getAntialiasedPen(const QPen& originalPen) const
{
    QPen enhancedPen = originalPen;

    enhancedPen.setCapStyle(Qt::RoundCap);
    enhancedPen.setJoinStyle(Qt::RoundJoin);

    if (originalPen.widthF() <= 1.5) {
        enhancedPen.setWidthF(originalPen.widthF() + 0.1);
    }

    return enhancedPen;
}


// ==================== OpenGL视口配置实现 ====================
void WhiteBoardWidget::setupOpenGLViewport()
{
    // 检查OpenGL是否可用
    if (!QOpenGLContext::openGLModuleType()) {
        qWarning() << "[OpenGL] OpenGL不可用，使用软件渲染";
        return;
    }

    QSurfaceFormat format = QSurfaceFormat::defaultFormat();

    if (format.samples() == 0) {
        format.setSamples(4);  // 4x MSAA
    }

    format.setDepthBufferSize(24);
    format.setStencilBufferSize(8);
    format.setSwapBehavior(QSurfaceFormat::DoubleBuffer);
    format.setSwapInterval(1);

    QOpenGLWidget* openglWidget = new QOpenGLWidget(this);
    openglWidget->setFormat(format);

    openglWidget->setAttribute(Qt::WA_AlwaysStackOnTop, false);
    openglWidget->setAttribute(Qt::WA_TranslucentBackground, true);

    setViewport(openglWidget);

    qDebug() << "[OpenGL] OpenGL视口配置成功";
    qDebug() << "[OpenGL] MSAA采样数:" << format.samples();
    qDebug() << "[OpenGL] 深度缓冲:" << format.depthBufferSize();
    qDebug() << "[OpenGL] 模板缓冲:" << format.stencilBufferSize();
}