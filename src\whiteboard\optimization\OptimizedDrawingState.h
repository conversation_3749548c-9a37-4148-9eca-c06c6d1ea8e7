#ifndef OPTIMIZEDDRAWINGSTATE_H
#define OPTIMIZEDDRAWINGSTATE_H

#include <QPointF>
#include <QPainterPath>
#include <QPen>
#include <QBrush>
#include <QRectF>
#include <QVector>
#include "DirtyRegionManager.h"
#include "../core/WhiteBoardTypes.h"
#include "../utils/DashPathConverter.h"

/**
 * @brief 优化的绘制状态管理器
 *
 * 核心功能：
 * 1. 集成增量路径构建和脏区域管理
 * 2. 提供统一的绘制状态接口
 * 3. 优化不同工具类型的绘制性能
 */
class OptimizedDrawingState
{
public:
    /**
     * @brief 路径段结构 - 缓存路径片段
     */
    struct PathSegment {
        QVector<QPointF> points;        // 路径点集合
        QRectF boundingRect;            // 边界矩形
        bool needsRebuild = true;       // 是否需要重建

        void addPoint(const QPointF& point);
        void clear();
        bool isEmpty() const { return points.isEmpty(); }
        int pointCount() const { return points.size(); }
    };

public:
    OptimizedDrawingState();
    ~OptimizedDrawingState() = default;

    // 基本属性
    void setToolType(ToolType toolType);
    void setPen(const QPen& pen);
    void setBrush(const QBrush& brush);
    
    ToolType getToolType() const { return m_toolType; }
    QPen getPen() const { return m_pen; }
    QBrush getBrush() const { return m_brush; }

    // 绘制操作
    void startDrawing(const QPointF& startPoint);
    void continueDrawing(const QPointF& point);
    void finishDrawing();
    void cancelDrawing();

    // 路径获取
    QPainterPath getCurrentPath() const;

    QRectF getCurrentBounds() const;
    
    // 脏区域管理
    QRectF getDirtyRegion();  // 获取增量脏区域
    bool hasDirtyRegions() const;
    void clearDirtyRegions();

    // 状态查询
    bool isDrawing() const { return m_isDrawing; }
    bool hasPath() const;
    bool needsUpdate() const;

    // 点位信息获取
    QPointF getCurrentPoint() const { return m_currentPoint; }
    QPointF getStartPoint() const { return m_startPoint; }
    QPointF getLastPoint() const { return m_lastPoint; }
    
    // 性能优化配置
    void setBatchSize(int size);

    // 增量路径构建方法 (原 IncrementalPathBuilder 功能)
    QRectF getIncrementalBounds() const;  // 获取自上次更新以来的边界
    void setDashConversionEnabled(bool enabled) { m_dashConversionEnabled = enabled; }
    bool isBuilding() const { return m_isBuilding; }
    bool needsRebuild() const { return m_needsRebuild; }
    int getPendingPointCount() const { return m_pendingSegment.pointCount(); }
    void rebuildPath();
    void clearCache();

private:
    // 基本状态
    ToolType m_toolType = ToolType::FreeDraw;
    QPen m_pen;
    QBrush m_brush;
    bool m_isDrawing = false;
    
    // 点位信息
    QPointF m_startPoint;
    QPointF m_currentPoint;
    QPointF m_lastPoint;
    
    DirtyRegionManager m_dirtyRegionManager;



    // 双缓冲脏区域管理
    QRectF m_lastDrawnRegion;      // 上一次绘制的区域
    QRectF m_currentDrawRegion;    // 当前绘制的区域

    // 配置
    bool m_dirtyRegionOptimizationEnabled = true;

    // 最小脏区域设置
    static constexpr qreal MIN_DIRTY_REGION_SIZE = 35.0;  // 最小脏区域尺寸（确保微小图形可见）
    
    // 内部方法
    void updateDirtyRegion(const QPointF& newPoint);
    void handleToolSpecificDrawing(const QPointF& point);
    void handleShapeToolDrawing(const QPointF& point, qreal margin);
    QRectF calculateShapeRegion(const QPointF& point, qreal margin);
    QRectF ensureMinimumRegionSize(const QRectF& region) const;


};

#endif // OPTIMIZEDDRAWINGSTATE_H
