#ifndef DRAWINGSTATE_H
#define DRAWINGSTATE_H

#include <QPointF>
#include <QPainterPath>
#include <QPen>
#include "../core/WhiteBoardTypes.h"

class DrawingState
{

public:
    DrawingState();
    ~DrawingState() = default;

    
    void setToolType(ToolType toolType);
    void setPen(const QPen& pen);
    void setBrush(const QBrush& brush);
    ToolType getToolType() const { return m_toolType; }
    QPen getPen() const { return m_pen; }
    QBrush getBrush() const { return m_brush; }
    bool isDrawing() const { return m_isDrawing; }

    // 绘制操作
    void startDrawing(const QPointF& startPoint);
    void continueDrawing(const QPointF& point);
    void finishDrawing(const QPointF& point);

    // 路径获取
    QPainterPath getCurrentPath() const;

    // 获取freedraw 未完成绘制的路径列表
    QPainterPath getFreeDrawPathUnfinished() const;
    
private:

    void addPoint(const QPointF& point);

    // 基本状态
    ToolType m_toolType = ToolType::FreeDraw;
    QPen m_pen;
    QBrush m_brush;
    bool m_isDrawing = false;

    QPointF m_startPoint;
    QPointF m_currentPoint;
    QPointF m_lastPoint;
    QPainterPath m_currentPath;
    QList<QPainterPath> m_freeDrawPathUnfinished;
};

#endif